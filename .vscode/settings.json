{"docker.dockerPath": "/usr/bin/podman", "docker.dockerComposePath": "/usr/bin/podman-compose", "docker.environment": {"DOCKER_HOST": "unix:///home/<USER>/.docker/docker.sock"}, "docker.host": "unix:///home/<USER>/.docker/docker.sock", "docker.machineNaming": "podman", "docker.enableDockerComposeLanguageService": true, "docker.showStartPage": false, "docker.attachShellCommand.linuxContainer": "/bin/bash", "docker.attachShellCommand.windowsContainer": "powershell", "files.associations": {"podman-compose.yml": "dockercompose", "podman-compose.yaml": "dockercompose", "Dockerfile": "dockerfile"}, "docker.containers.groupBy": "None", "docker.containers.label": "ContainerName", "docker.containers.description": ["ContainerName", "Status"], "docker.images.groupBy": "None", "docker.images.label": "Tag", "docker.images.description": ["CreatedTime", "Size"], "docker.commands.build": "/usr/bin/podman build", "docker.commands.compose": "/usr/bin/podman-compose", "docker.commands.composeParallel": true, "docker.promptOnSystemPrune": true, "docker.truncateLongRegistryPaths": false, "docker.truncateMaxLength": 10, "docker.dockerodeOptions": {"socketPath": "/home/<USER>/.docker/docker.sock"}}