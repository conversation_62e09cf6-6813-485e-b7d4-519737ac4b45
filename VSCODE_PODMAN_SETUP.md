# VS Code Container Tools s <PERSON><PERSON><PERSON> na Fedora 42

Tento dokument popisuje, jak nakonfigurovat VS Code Container Tools rozšíření pro práci s Podmanem místo Dockeru na Fedora 42.

## Problém

Container Tools for Visual Studio Code rozšíření standardně o<PERSON><PERSON><PERSON>, ale na Fedora 42 pou<PERSON><PERSON><PERSON><PERSON><PERSON>. Bez správné konfigurace rozšíření nebude schopno zobrazit kontejnery a images.

## Řešení

### 1. Ověření instalace Podmanu

```bash
# Ověření, že je podman nainstalován
which podman
podman --version

# Ov<PERSON>ření, že je podman-compose nainstalován
which podman-compose
podman-compose --version
```

### 2. Aktivace Podman socketu

```bash
# Spuštění a aktivace podman socketu pro uživatele
systemctl --user enable podman.socket
systemctl --user start podman.socket

# Ově<PERSON><PERSON><PERSON> stavu
systemctl --user status podman.socket
```

### 3. Konfigurace VS Code

Soubor `.vscode/settings.json` je ji<PERSON> nakonfigurován s následujícími nastaveními:

```json
{
    "docker.dockerPath": "podman",
    "docker.dockerComposePath": "podman-compose",
    "docker.environment": {
        "DOCKER_HOST": "unix:///run/user/1000/podman/podman.sock"
    },
    "docker.host": "unix:///run/user/1000/podman/podman.sock",
    "docker.machineNaming": "podman",
    "docker.enableDockerComposeLanguageService": true,
    "docker.showStartPage": false,
    "docker.attachShellCommand.linuxContainer": "/bin/bash",
    "docker.attachShellCommand.windowsContainer": "powershell",
    "files.associations": {
        "podman-compose.yml": "dockercompose",
        "podman-compose.yaml": "dockercompose",
        "Dockerfile": "dockerfile"
    }
}
```

### 4. Doporučená rozšíření

Soubor `.vscode/extensions.json` obsahuje doporučená rozšíření včetně:

- `ms-vscode.vscode-docker` - Container Tools
- `ms-vscode-remote.remote-containers` - Dev Containers
- `redhat.vscode-yaml` - YAML podpora

### 5. Užitečné příkazy

#### Spuštění služeb
```bash
# Pomocí skriptu
./start_services.sh

# Nebo přímo
podman-compose up -d
```

#### Kontrola stavu
```bash
# Pomocí skriptu
./check_status.sh

# Nebo přímo
podman-compose ps
podman ps -a
```

#### Zastavení služeb
```bash
# Pomocí skriptu
./stop_services.sh

# Nebo přímo
podman-compose down
```

### 6. VS Code Tasks

Soubor `.vscode/tasks.json` obsahuje předkonfigurované úlohy:

- **NESTOR: Build All Services** - Sestavení všech služeb
- **NESTOR: Start All Services** - Spuštění všech služeb
- **NESTOR: Stop All Services** - Zastavení všech služeb
- **NESTOR: Check Status** - Kontrola stavu služeb
- **NESTOR: Health Check** - Kontrola zdraví služeb
- **NESTOR: View Logs** - Zobrazení logů

### 7. Debugging

Soubor `.vscode/launch.json` obsahuje konfigurace pro připojení k jednotlivým kontejnerům:

- **Docker: Attach to Container** - Připojení k API kontejneru
- **Docker: Attach to MCP** - Připojení k Memory Context Processor
- **Docker: Attach to RAG** - Připojení k RAG Service
- **Docker: Attach to LLM** - Připojení k LLM Service

## Řešení problémů

### Container Tools nevidí kontejnery

1. Ověřte, že je podman socket aktivní:
   ```bash
   systemctl --user status podman.socket
   ```

2. Ověřte, že socket existuje:
   ```bash
   ls -la /run/user/1000/podman/podman.sock
   ```

3. Restartujte VS Code

### Kontejnery se nezobrazují v Docker view

1. Otevřete Command Palette (Ctrl+Shift+P)
2. Spusťte "Docker: Refresh"
3. Nebo restartujte VS Code

### Chyby při připojování k kontejnerům

1. Ověřte, že kontejnery běží:
   ```bash
   podman ps
   ```

2. Ověřte porty:
   ```bash
   podman port <container_name>
   ```

## Výhody této konfigurace

- ✅ Plná kompatibilita s Container Tools rozšířením
- ✅ Podpora pro podman-compose soubory
- ✅ Debugging kontejnerů přímo z VS Code
- ✅ Integrované úlohy pro správu služeb
- ✅ Automatické rozpoznání Dockerfile a compose souborů
- ✅ Bezpečnost Podmanu (rootless kontejnery)

## Další informace

- [Podman dokumentace](https://docs.podman.io/)
- [VS Code Container Tools](https://marketplace.visualstudio.com/items?itemName=ms-vscode.vscode-docker)
- [Podman Compose](https://github.com/containers/podman-compose)
