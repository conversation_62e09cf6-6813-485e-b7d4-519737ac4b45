#!/bin/bash

# NESTOR Services Stop Script
# Tento skript zastaví všechny služby pomocí podman-compose

echo "🛑 Zastavování NESTOR služeb..."

# <PERSON><PERSON><PERSON><PERSON>, zda je podman-compose dostupný
if ! command -v podman-compose &> /dev/null; then
    echo "❌ podman-compose není nainstal<PERSON>n"
    exit 1
fi

# Zastavení všech služeb
echo "🛑 Zastavování všech služeb..."
podman-compose down

# Zobrazení stavu
echo "📊 Stav služeb:"
podman-compose ps

echo "✅ Všechny služby zastaveny!"
