#!/bin/bash

# NESTOR Services Startup Script
# Tento skript spustí všechny služby pomocí podman

echo "🚀 Spouštění NESTOR služeb..."

# Spuštění databáze a Redis (už běž<PERSON>)
echo "🗄️ Kontrola databáze a Redis..."
podman ps | grep nestor-postgres-new || echo "❌ PostgreSQL neběží"
podman ps | grep nestor-redis || echo "❌ Redis neběží"

# Čekání na spuštění databáze
echo "⏳ Čekání na spuštění databáze..."
sleep 5

# Spuštění pgvector rozšíření
echo "🔧 Aktivace pgvector rozšíření..."
podman exec nestor-postgres-new psql -U nestor -d nestor -c "CREATE EXTENSION IF NOT EXISTS vector;" || echo "⚠️ Rozšíření už je aktivní nebo databáze není připrave<PERSON>"

# Spuštění API služby
echo "🚀 Spouštění API služby..."
podman run -d --name nestor-api-simple \
  -p 8000:8000 \
  -e DATABASE_URL=************************************************************/nestor \
  -e REDIS_URL=redis://nestor-redis:6379 \
  --link nestor-postgres-new:postgres \
  --link nestor-redis:redis \
  python:3.11-slim \
  bash -c "pip install fastapi uvicorn[standard] pydantic && echo 'from fastapi import FastAPI; app = FastAPI(); @app.get(\"/health\"); def health(): return {\"status\": \"ok\"}' > main.py && uvicorn main:app --host 0.0.0.0 --port 8000"

# Zobrazení stavu
echo "📊 Stav služeb:"
podman ps

echo "✅ Základní služby spuštěny!"
echo "📊 Přístup k službám:"
echo "  - API: http://localhost:8000"
echo "  - PostgreSQL: localhost:5433"
echo "  - Redis: localhost:6379"

echo ""
echo "🔍 Pro kontrolu zdraví služeb spusťte:"
echo "  curl http://localhost:8000/health"
