#!/bin/bash

# NESTOR Services Status Check Script
# Tento skript zkontroluje stav všech služeb

echo "📊 Kontrola stavu NESTOR služeb..."

# <PERSON><PERSON><PERSON><PERSON>, zda je podman-compose dostupný
if ! command -v podman-compose &> /dev/null; then
    echo "❌ podman-compose není nainstal<PERSON>n"
    exit 1
fi

# Zobrazení stavu kontejnerů
echo "🐳 Stav kontejnerů:"
podman-compose ps

echo ""
echo "🔍 Kontrola zdraví služeb:"

# Funkce pro kontrolu zdraví služby
check_health() {
    local service_name=$1
    local port=$2
    local url="http://localhost:${port}/health"
    
    echo -n "  ${service_name} (port ${port}): "
    
    if curl -s -f "${url}" > /dev/null 2>&1; then
        echo "✅ OK"
    else
        echo "❌ NEDOSTUPNÁ"
    fi
}

# Kontrola jednotlivých služeb
check_health "API" "8000"
check_health "Memory Context Processor" "8001"
check_health "RAG Service" "8002"
check_health "LLM Service" "8003"

echo ""
echo "🗄️ Kontrola databáze:"
echo -n "  PostgreSQL (port 5432): "
if podman-compose exec postgres pg_isready -U nestor > /dev/null 2>&1; then
    echo "✅ OK"
else
    echo "❌ NEDOSTUPNÁ"
fi

echo -n "  Redis (port 6379): "
if podman-compose exec redis redis-cli ping > /dev/null 2>&1; then
    echo "✅ OK"
else
    echo "❌ NEDOSTUPNÁ"
fi

echo ""
echo "📈 Využití zdrojů:"
podman stats --no-stream --format "table {{.Name}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.MemPerc}}" 2>/dev/null || echo "  Statistiky nejsou dostupné"
